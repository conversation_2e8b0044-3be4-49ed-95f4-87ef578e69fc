package database

import (
	"file-manager/models"

	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
	_ "modernc.org/sqlite" // 纯Go SQLite驱动
)

func Initialize(databaseURL string) (*gorm.DB, error) {
	// 使用纯Go SQLite驱动，无需CGO
	db, err := gorm.Open(sqlite.Dialector{
		DriverName: "sqlite",
		DSN:        databaseURL,
	}, &gorm.Config{
		Logger: logger.Default.LogMode(logger.Info),
	})
	if err != nil {
		return nil, err
	}

	// 自动迁移数据库表
	err = db.AutoMigrate(
		&models.User{},
		&models.File{},
		&models.Directory{},
		&models.RenameOperation{},
		&models.OperationLog{},
	)
	if err != nil {
		return nil, err
	}

	return db, nil
}
